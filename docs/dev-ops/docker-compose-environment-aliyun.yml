# docker-compose -f docker-compose-environment-aliyun.yml up -d
version: '3'
services:
  # 对话模型
  # ollama pull deepseek-r1:1.5b
  # 运行模型
  # ollama run deepseek-r1:1.5b
  # 联网模型
  # ollama pull nomic-embed-text
  ollama:
    image: registry.cn-hangzhou.aliyuncs.com/xfg-studio/ollama:0.5.10
    container_name: ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    networks:
      - my-network

  redis:
    image: registry.cn-hangzhou.aliyuncs.com/xfg-studio/redis:6.2
    container_name: redis
    restart: always
    hostname: redis
    privileged: true
    ports:
      - 16379:6379
    volumes:
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - my-network
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 3
  # RedisAdmin https://github.com/joeferner/redis-commander
  # 账密 admin/admin
  redis-admin:
    image: registry.cn-hangzhou.aliyuncs.com/xfg-studio/redis-commander:0.8.0
    container_name: redis-admin
    hostname: redis-commander
    restart: always
    ports:
      - 8081:8081
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin
      - LANG=C.UTF-8
      - LANGUAGE=C.UTF-8
      - LC_ALL=C.UTF-8
    networks:
      - my-network
    depends_on:
      redis:
        condition: service_healthy

  vector_db:
    image: registry.cn-hangzhou.aliyuncs.com/xfg-studio/pgvector:v0.5.0
    container_name: vector_db
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=ai-rag-knowledge
      - PGPASSWORD=postgres
    volumes:
      - ./pgvector/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    logging:
      options:
        max-size: 10m
        max-file: "3"
    ports:
      - '15432:5432'
    healthcheck:
      test: "pg_isready -U postgres -d ai-rag-knowledge"
      interval: 2s
      timeout: 20s
      retries: 10
    networks:
      - my-network
      
  # pg 管理工具
  pgadmin:
    image: registry.cn-hangzhou.aliyuncs.com/xfg-studio/pgadmin4:9.1.0
    container_name: vector_db_admin
    restart: always
    ports:
      - "5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    depends_on:
      - vector_db
    networks:
      - my-network

networks:
  my-network:
    driver: bridge
