.chat-item {
       @apply flex items-center gap-2 p-2 hover:bg-gray-100 rounded cursor-pointer transition-colors;
   }
.chat-item.selected {
    @apply bg-blue-50; /* 或使用深色背景如bg-gray-200 */
}
.chat-item-content {
       @apply flex-1 truncate;
   }
.chat-actions {
       @apply flex items-center justify-end opacity-0 transition-opacity w-8;
   }
.chat-item:hover.chat-actions {
       @apply opacity-100;
   }
.context-menu {
       @apply absolute bg-white border rounded-lg shadow-lg py-1 z-50;
       min-width: 120px;
   }
.context-menu-item {
       @apply px-4 py-2 hover:bg-gray-100 text-sm flex items-center gap-2;
   }

.markdown-body {
      font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;
      font-size: 16px;
      line-height: 1.5;
      word-wrap: break-word;
  }
.markdown-body pre {
      background-color: #f6f8fa;
      border-radius: 6px;
      padding: 16px;
      overflow-x: auto;
  }
.markdown-body code {
      background-color: rgba(175,184,193,0.2);
      border-radius: 6px;
      padding: 0.2em 0.4em;
      font-size: 85%;
  }
.markdown-body pre code {
      background-color: transparent;
      padding: 0;
      font-size: 100%;
  }
.markdown-body h1,.markdown-body h2,.markdown-body h3,
.markdown-body h4,.markdown-body h5,.markdown-body h6 {
      margin-top: 24px;
      margin-bottom: 16px;
      font-weight: 600;
      line-height: 1.25;
  }
.markdown-body h1 { font-size: 2em; }
.markdown-body h2 { font-size: 1.5em; }
.markdown-body h3 { font-size: 1.25em; }
.markdown-body ul,.markdown-body ol {
      padding-left: 2em;
  }
.markdown-body ul { list-style-type: disc; }
.markdown-body ol { list-style-type: decimal; }
.markdown-body table {
      border-spacing: 0;
      border-collapse: collapse;
      margin-top: 0;
      margin-bottom: 16px;
  }
.markdown-body table th,.markdown-body table td {
      padding: 6px 13px;
      border: 1px solid #d0d7de;
  }
.markdown-body table tr:nth-child(2n) {
      background-color: #f6f8fa;
  }
.chat-actions button {
      margin-left: 4px; /* Add some space between buttons */
  }
  /* 下拉菜单过渡动画 */
  #uploadMenu {
      transition: all 0.2s ease-out;
      transform-origin: top right;
  }

  #uploadMenu a {
      transition: background-color 0.2s ease;
  }
