version: '3.8'
# docker-compose -f docker-compose-app-v1.0.yml up -d
services:
  # 部署前端项目
  nginx:
    image: registry.cn-hangzhou.aliyuncs.com/xfg-studio/nginx:latest
    container_name: nginx
    restart: always
    ports:
      - '80:80'
    volumes:
      - ./nginx/html:/usr/share/nginx/html
      - ./nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf/conf.d:/etc/nginx/conf.d
    privileged: true
  # ai-rag-knowledge
  ai-rag-knowledge-app:
    image: crpi-wcl5qyobymekhl28.cn-hangzhou.personal.cr.aliyuncs.com/appleweb_c/apple-ai-rag:6.8
    container_name: ai-rag-knowledge-app
    restart: on-failure
    ports:
      - "8090:8090"
    environment:
      - TZ=PRC
      - SERVER_PORT=8090
      - SPRING_DATASOURCE_USERNAME=postgres
      - SPRING_DATASOURCE_PASSWORD=postgres
      - SPRING_DATASOURCE_URL=******************************************************
      - SPRING_DATASOURCE_DRIVER_CLASS_NAME=org.postgresql.Driver
      - SPRING_AI_OLLAMA_BASE_URL=http://*************:11434
      - SPRING_AI_OLLAMA_EMBEDDING_OPTIONS_NUM_BATCH=512
      - SPRING_AI_OLLAMA_MODEL=nomic-embed-text
      - SPRING_AI_OPENAI_BASE_URL=https://pro-share-aws-api.zcyai.com/
      - SPRING_AI_OPENAI_API_KEY=sk-eEyfxptPgbfXd3Z164260740E0494161Bd8957E918Ed7c4d
      - SPRING_AI_OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
      - SPRING_AI_RAG_EMBED=nomic-embed-text
      - REDIS_SDK_CONFIG_HOST=*************
      - REDIS_SDK_CONFIG_PORT=16379
    volumes:
      - ./log:/data/log
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - my-network

networks:
  my-network:
    driver: bridge
